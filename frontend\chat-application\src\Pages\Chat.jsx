import { useEffect, useRef, useState } from 'react';
import axios from 'axios';

const Chat = () => {
  const [token, setToken] = useState(null);
  const [user, setUser] = useState(null);
  const [allUsers, setAllUsers] = useState([]);
  const [showAllUsers, setShowAllUsers] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [chatHistory, setChatHistory] = useState([]);
  const [message, setMessage] = useState('');
  const messagesEndRef = useRef(null);

  // ✅ Load token & user from localStorage on mount
  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (token && userData) {
      const parsedUser = JSON.parse(userData);
      setToken(token);
      setUser(parsedUser);
    }
  }, []);

const fetchAllUsers = async () => {
  if (!user?._id || !token) {
    console.warn('User or token not ready');
    return;
  }

  try {
    const res = await axios.get(`http://localhost:3000/api/users/all/${user._id}`);
    const users = res.data.filter((u) => u._id !== user._id);
    setAllUsers(users);
    setShowAllUsers(true);
  } catch (err) {
    console.error('Failed to fetch users:', err);
  }
};


  const fetchChatHistory = async (u) => {
    if (!user?._id || !u?._id) return;
    setSelectedUser(u);

    try {
      const res = await axios.get(`http://localhost:3000/api/chat/history/${user._id}/${u._id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      setChatHistory(res.data);
    } catch (err) {
      console.error('Failed to fetch chat history:', err);
    }
  };

  const sendMessage = async (e) => {
    e.preventDefault();
    if (!message.trim()) return;

    try {
      const res = await axios.post(`http://localhost:3000/api/chat/send`, {
        sender: user._id,
        receiver: selectedUser._id,
        message,
      });
      setChatHistory((prev) => [...prev, res.data.chat]);
      setMessage('');
    } catch (err) {
      console.error('Failed to send message:', err);
    }
  };

  // if (!user) return <p>Loading user...</p>;

  return (
    <div style={{ display: 'flex', height: '100vh' }}>
      <div style={{ width: '300px', borderRight: '1px solid #ccc', padding: '1rem' }}>
        <h3>Hello, {user?.username || 'Guest'}</h3>


        <button onClick={() => { fetchAllUsers(); setShowAllUsers(true); }}>
          Start new Chat
        </button>

        <div style={{ marginTop: '1rem' }}>
          <h4>{showAllUsers ? 'All Users' : 'Conversations'}</h4>
          {(showAllUsers ? allUsers : []).map((u) => (
            <div
              key={u._id}
              style={{
                padding: '8px',
                backgroundColor: selectedUser?._id === u._id ? '#ddd' : '#f5f5f5',
                marginBottom: '5px',
                cursor: 'pointer',
              }}
              onClick={() => fetchChatHistory(u)}
            >
              {u.username}
            </div>
          ))}
        </div>
      </div>

      <div style={{ flex: 1, padding: '1rem', display: 'flex', flexDirection: 'column' }}>
        <div style={{ flex: 1, overflowY: 'auto', borderBottom: '1px solid #ccc' }}>
          {selectedUser ? (
            <>
              <h3>Chat with {selectedUser.username}</h3>
              {chatHistory.map((msg, index) => (
                <div
                  key={index}
                  style={{
                    textAlign: msg.sender === user._id ? 'right' : 'left',
                  }}
                >
                  <p>
                    <strong>{msg.sender === user._id ? 'You' : selectedUser.username}:</strong> {msg.message}
                  </p>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </>
          ) : (
            <p>Select a conversation or start a new one</p>
          )}
        </div>

        {selectedUser && (
          <form onSubmit={sendMessage} style={{ display: 'flex', marginTop: '1rem' }}>
            <input
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              style={{ flex: 1, padding: '0.5rem' }}
              placeholder="Type a message"
            />
            <button type="submit">Send</button>
          </form>
        )}
      </div>
    </div>
  );
};

export default Chat;