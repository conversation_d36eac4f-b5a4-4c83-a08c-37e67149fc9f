import { useEffect, useRef, useState } from 'react';
import axios from 'axios';
import io from 'socket.io-client';

const Chat = () => {
  const [token, setToken] = useState(null);
  const [user, setUser] = useState(null);
  const [allUsers, setAllUsers] = useState([]);
  const [showAllUsers, setShowAllUsers] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [chatHistory, setChatHistory] = useState([]);
  const [message, setMessage] = useState('');
  const [onlineUsers, setOnlineUsers] = useState([]);
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState({});
  const messagesEndRef = useRef(null);
  const socketRef = useRef(null);

  // ✅ Load token & user from localStorage on mount
  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (token && userData) {
      const parsedUser = JSON.parse(userData);
      setToken(token);
      setUser(parsedUser);
    }
  }, []);

  // ✅ Socket.IO connection and real-time features
  useEffect(() => {
    if (user?._id) {
      // Initialize socket connection
      socketRef.current = io('http://localhost:3000');

      // Emit user connected event
      socketRef.current.emit('user_connected', user._id);

      // Listen for online users updates
      socketRef.current.on('update_online_users', (users) => {
        setOnlineUsers(users);
      });

      // Listen for incoming messages
      socketRef.current.on('receive_message', (messageData) => {
        // Only add message if it's for the current conversation
        if (selectedUser &&
            ((messageData.sender === user._id && messageData.receiver === selectedUser._id) ||
             (messageData.sender === selectedUser._id && messageData.receiver === user._id))) {
          setChatHistory(prev => [...prev, messageData]);
        }
      });

      // Listen for typing indicators
      socketRef.current.on('user_typing', ({ userId, isTyping: typing }) => {
        setTypingUsers(prev => ({
          ...prev,
          [userId]: typing
        }));
      });

      // Cleanup on unmount
      return () => {
        if (socketRef.current) {
          socketRef.current.disconnect();
        }
      };
    }
  }, [user, selectedUser]);

const fetchAllUsers = async () => {
  if (!user?._id || !token) {
    console.warn('User or token not ready');
    return;
  }

  try {
    const res = await axios.get(`http://localhost:3000/api/users/all/${user._id}`);
    const users = res.data.filter((u) => u._id !== user._id);
    setAllUsers(users);
    setShowAllUsers(true);
  } catch (err) {
    console.error('Failed to fetch users:', err);
  }
};


  const fetchChatHistory = async (u) => {
    if (!user?._id || !u?._id) return;
    setSelectedUser(u);

    try {
      const res = await axios.get(`http://localhost:3000/api/chat/history/${user._id}/${u._id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      setChatHistory(res.data);
    } catch (err) {
      console.error('Failed to fetch chat history:', err);
    }
  };

  const sendMessage = async (e) => {
    e.preventDefault();
    if (!message.trim()) return;

    const messageData = {
      sender: user._id,
      receiver: selectedUser._id,
      message: message.trim(),
      timestamp: new Date().toISOString(),
      senderName: user.username
    };

    try {
      // Save to database
      const res = await axios.post(`http://localhost:3000/api/chat/send`, {
        sender: user._id,
        receiver: selectedUser._id,
        message: message.trim(),
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Emit real-time message via Socket.IO
      if (socketRef.current) {
        socketRef.current.emit('send_message', messageData);
      }

      // Add to local chat history immediately
      setChatHistory((prev) => [...prev, { ...messageData, _id: res.data.chat._id }]);
      setMessage('');

      // Stop typing indicator
      if (socketRef.current) {
        socketRef.current.emit('typing', {
          userId: user._id,
          receiverId: selectedUser._id,
          isTyping: false
        });
      }
    } catch (err) {
      console.error('Failed to send message:', err);
    }
  };

  // Handle typing indicator
  const handleTyping = (e) => {
    setMessage(e.target.value);

    if (socketRef.current && selectedUser) {
      if (!isTyping) {
        setIsTyping(true);
        socketRef.current.emit('typing', {
          userId: user._id,
          receiverId: selectedUser._id,
          isTyping: true
        });
      }

      // Clear typing after 1 second of no typing
      clearTimeout(window.typingTimer);
      window.typingTimer = setTimeout(() => {
        setIsTyping(false);
        socketRef.current.emit('typing', {
          userId: user._id,
          receiverId: selectedUser._id,
          isTyping: false
        });
      }, 1000);
    }
  };

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatHistory]);

  if (!user) return <div style={{ padding: '2rem', textAlign: 'center' }}>Loading user...</div>;

  return (
    <div style={{ display: 'flex', height: '100vh' }}>
      <div style={{ width: '300px', borderRight: '1px solid #ccc', padding: '1rem' }}>
        <h3>Hello, {user?.username || 'Guest'}</h3>


        <button onClick={() => { fetchAllUsers(); setShowAllUsers(true); }}>
          Start new Chat
        </button>

        <div style={{ marginTop: '1rem' }}>
          <h4>{showAllUsers ? 'All Users' : 'Conversations'}</h4>
          {(showAllUsers ? allUsers : []).map((u) => (
            <div
              key={u._id}
              style={{
                padding: '8px',
                backgroundColor: selectedUser?._id === u._id ? '#ddd' : '#f5f5f5',
                marginBottom: '5px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
              onClick={() => fetchChatHistory(u)}
            >
              <span>{u.username}</span>
              <span
                style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  backgroundColor: onlineUsers.includes(u._id) ? '#4CAF50' : '#ccc',
                  display: 'inline-block',
                }}
                title={onlineUsers.includes(u._id) ? 'Online' : 'Offline'}
              />
            </div>
          ))}
        </div>
      </div>

      <div style={{ flex: 1, padding: '1rem', display: 'flex', flexDirection: 'column' }}>
        <div style={{ flex: 1, overflowY: 'auto', borderBottom: '1px solid #ccc' }}>
          {selectedUser ? (
            <>
              <h3>Chat with {selectedUser.username}</h3>
              {chatHistory.map((msg, index) => (
                <div
                  key={msg._id || index}
                  style={{
                    textAlign: msg.sender === user._id ? 'right' : 'left',
                    marginBottom: '10px',
                  }}
                >
                  <div
                    style={{
                      display: 'inline-block',
                      maxWidth: '70%',
                      padding: '8px 12px',
                      borderRadius: '18px',
                      backgroundColor: msg.sender === user._id ? '#007bff' : '#e9ecef',
                      color: msg.sender === user._id ? 'white' : 'black',
                    }}
                  >
                    <p style={{ margin: 0, wordBreak: 'break-word' }}>
                      {msg.message}
                    </p>
                    <small
                      style={{
                        fontSize: '0.75rem',
                        opacity: 0.7,
                        display: 'block',
                        marginTop: '4px',
                      }}
                    >
                      {msg.timestamp ? new Date(msg.timestamp).toLocaleTimeString() :
                       msg.createdAt ? new Date(msg.createdAt).toLocaleTimeString() : ''}
                    </small>
                  </div>
                </div>
              ))}

              {/* Typing indicator */}
              {typingUsers[selectedUser?._id] && (
                <div style={{ textAlign: 'left', marginBottom: '10px' }}>
                  <div
                    style={{
                      display: 'inline-block',
                      padding: '8px 12px',
                      borderRadius: '18px',
                      backgroundColor: '#e9ecef',
                      fontStyle: 'italic',
                      color: '#666',
                    }}
                  >
                    {selectedUser.username} is typing...
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </>
          ) : (
            <p>Select a conversation or start a new one</p>
          )}
        </div>

        {selectedUser && (
          <form onSubmit={sendMessage} style={{ display: 'flex', marginTop: '1rem', gap: '8px' }}>
            <input
              value={message}
              onChange={handleTyping}
              style={{
                flex: 1,
                padding: '12px',
                borderRadius: '20px',
                border: '1px solid #ddd',
                outline: 'none',
                fontSize: '14px'
              }}
              placeholder={`Message ${selectedUser.username}...`}
              autoComplete="off"
            />
            <button
              type="submit"
              disabled={!message.trim()}
              style={{
                padding: '12px 20px',
                borderRadius: '20px',
                border: 'none',
                backgroundColor: message.trim() ? '#007bff' : '#ccc',
                color: 'white',
                cursor: message.trim() ? 'pointer' : 'not-allowed',
                fontSize: '14px',
                fontWeight: 'bold'
              }}
            >
              Send
            </button>
          </form>
        )}
      </div>
    </div>
  );
};

export default Chat;