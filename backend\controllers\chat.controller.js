const ChatModel = require('../models/chat.model');
const UserModel = require('../models/user.model');
const { validationResult } = require('express-validator');

// @desc    Send a message
// @route   POST /api/chat/send
// @access  Private
const sendMessage = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) return res.status(400).json({ errors: errors.array() });

    const { sender, receiver, message} = req.body;

    const newChat = await ChatModel.create({ sender, receiver, message });
await newChat.populate('sender', 'username');
res.status(201).json({ message: 'Message sent', chat: newChat });

  } catch (error) {
    res.status(500).json({ message: 'Message send failed', error });
  }
};

// @desc    Get chat history between two users
// @route   GET /api/chat/history/:user1/:user2
// @access  Private
const getChatHistory = async (req, res) => {
  try {
    const { user1, user2 } = req.params;

    const chatHistory = await ChatModel.find({
      $or: [
        { sender: user1, receiver: user2 },
        { sender: user2, receiver: user1 }
      ]
    }).sort({ createdAt: 1 })// oldest to newest
    .populate('sender', 'username')
.populate('receiver', 'username');

    res.status(200).json(chatHistory);
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch chat history', error });
  }
};

// @desc    Get all conversations of a user with last message
// @route   GET /api/chat/conversations/:userId
// @access  Private
const getAllConversations = async (req, res) => {
  try {
    const { userId } = req.params;

    // Get all conversations for the user
    const conversations = await ChatModel.aggregate([
      {
        $match: {
          $or: [{ sender: userId }, { receiver: userId }]
        }
      },
      {
        $sort: { createdAt: -1 }
      },
      {
        $group: {
          _id: {
            $cond: [
              { $eq: ['$sender', userId] },
              '$receiver',
              '$sender'
            ]
          },
          lastMessage: { $first: '$message' },
          lastMessageTime: { $first: '$createdAt' },
          lastMessageSender: { $first: '$sender' },
          unreadCount: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $ne: ['$sender', userId] },
                    { $eq: ['$seen', false] }
                  ]
                },
                1,
                0
              ]
            }
          }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'userInfo'
        }
      },
      {
        $unwind: '$userInfo'
      },
      {
        $project: {
          _id: '$userInfo._id',
          username: '$userInfo.username',
          email: '$userInfo.email',
          online: '$userInfo.online',
          lastMessage: 1,
          lastMessageTime: 1,
          lastMessageSender: 1,
          unreadCount: 1
        }
      },
      {
        $sort: { lastMessageTime: -1 }
      }
    ]);

    res.status(200).json(conversations);
  } catch (error) {
    res.status(500).json({ message: 'Failed to get conversations', error });
  }
};

// @desc    Delete a message by ID
// @route   DELETE /api/chat/delete/:messageId
// @access  Private
const deleteMessage = async (req, res) => {
  try {
    const { messageId } = req.params;

    const deleted = await ChatModel.findByIdAndDelete(messageId);

    if (!deleted) return res.status(404).json({ message: 'Message not found' });

    res.status(200).json({ message: 'Message deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Failed to delete message', error });
  }
};

module.exports = {
  sendMessage,
  getChatHistory,
  getAllConversations,
  deleteMessage,
};
